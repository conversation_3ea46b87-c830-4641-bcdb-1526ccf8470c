import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token and get the user ID
      const { userId } = await verifyJWT(token)

      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      // Try to use MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB
        const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })
        console.log(
          '[School Profile API] MongoDB user found:',
          mongoUser ? 'yes' : 'no',
          'userId:',
          userId,
        )

        if (mongoUser) {
          // Check if user is a school admin, mentor, teacher, or super admin
          let userRole = null
          console.log('[School Profile API] MongoDB user role raw:', mongoUser.role)

          if (mongoUser.role && typeof mongoUser.role === 'object' && mongoUser.role.slug) {
            userRole = mongoUser.role.slug
            console.log('[School Profile API] MongoDB user role from object:', userRole)
          } else if (typeof mongoUser.role === 'string') {
            // Try to look up the role document if it's an ObjectId string
            try {
              const roleDoc = await db
                .collection('roles')
                .findOne({ _id: new ObjectId(mongoUser.role) })
              console.log('[School Profile API] MongoDB role lookup:', roleDoc)
              userRole = roleDoc?.slug || mongoUser.role
            } catch (error) {
              console.error('[School Profile API] Error in role lookup:', error)
              userRole = mongoUser.role
            }
            console.log('[School Profile API] MongoDB user role from string:', userRole)
          } else if (mongoUser.role && typeof mongoUser.role === 'object') {
            // Handle role as ObjectId in various formats
            try {
              // Handle EJSON format with $oid
              if (mongoUser.role.$oid) {
                const roleDoc = await db
                  .collection('roles')
                  .findOne({ _id: new ObjectId(mongoUser.role.$oid) })
                console.log('[School Profile API] MongoDB role lookup from $oid:', roleDoc)
                userRole = roleDoc?.slug
              }
              // Handle direct ObjectId instance
              else if (
                mongoUser.role instanceof ObjectId ||
                (mongoUser.role.constructor && mongoUser.role.constructor.name === 'ObjectId')
              ) {
                const roleId = mongoUser.role.toString()
                console.log('[School Profile API] MongoDB role is direct ObjectId, ID:', roleId)
                const roleDoc = await db.collection('roles').findOne({ _id: new ObjectId(roleId) })
                console.log(
                  '[School Profile API] MongoDB role lookup from ObjectId instance:',
                  roleDoc,
                )
                userRole = roleDoc?.slug
              }
              // Handle _id field in embedded document
              else if (mongoUser.role._id) {
                const roleIdValue =
                  mongoUser.role._id instanceof ObjectId
                    ? mongoUser.role._id.toString()
                    : mongoUser.role._id.$oid || mongoUser.role._id
                console.log('[School Profile API] MongoDB role has _id field:', roleIdValue)
                const roleDoc = await db
                  .collection('roles')
                  .findOne({ _id: new ObjectId(roleIdValue) })
                console.log('[School Profile API] MongoDB role lookup from _id field:', roleDoc)
                userRole = roleDoc?.slug
              }
            } catch (error) {
              console.error('[School Profile API] Error in EJSON/ObjectId role lookup:', error)
            }
          }
          console.log('[School Profile API] Final user role check:', userRole)

          // Modified condition to handle null roles differently
          if (!userRole) {
            console.log('[School Profile API] Permission denied: Role is null')
            return NextResponse.json({ error: 'Role not found or invalid' }, { status: 403 })
          }

          if (!['school-admin', 'mentor', 'teacher', 'super-admin'].includes(userRole)) {
            console.log(
              '[School Profile API] Permission denied: User role is not allowed:',
              userRole,
            )
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get school ID from MongoDB EJSON format
          let schoolId = null
          console.log('[School Profile API] School data:', mongoUser.school)

          // Handle different ways school might be represented
          if (!mongoUser.school) {
            console.log('[School Profile API] No school found for user')
          } else if (typeof mongoUser.school === 'string') {
            schoolId = mongoUser.school
          } else if (typeof mongoUser.school === 'object') {
            // Use 'any' type for MongoDB data that TypeScript doesn't recognize
            const schoolObj = mongoUser.school as any

            // Check if it's in MongoDB EJSON format with $oid
            if (schoolObj.$oid) {
              schoolId = schoolObj.$oid
              console.log('[School Profile API] Found school ID in $oid format:', schoolId)
            } else if (schoolObj._id) {
              // Handle if it's a full embedded document
              const idField = schoolObj._id as any
              schoolId =
                typeof idField === 'object' && idField.$oid ? idField.$oid : String(idField)
              console.log('[School Profile API] Found school ID in _id field:', schoolId)
            } else if (schoolObj.id) {
              // Handle Payload format - convert Buffer to ObjectId string if needed
              if (schoolObj.id instanceof Buffer) {
                // Convert Buffer to ObjectId string
                schoolId = new ObjectId(schoolObj.id).toString()
              } else {
                schoolId = schoolObj.id.toString()
              }
              console.log('[School Profile API] Found school ID in id field:', schoolId)
            } else {
              // Handle ObjectId instance directly
              schoolId = schoolObj.toString()
              console.log('[School Profile API] Found school ID by toString:', schoolId)
            }
          }

          console.log('[School Profile API] Final school ID:', schoolId)

          if (!schoolId || !ObjectId.isValid(schoolId)) {
            console.error('[School Profile API] Invalid or missing school ID')
            return NextResponse.json(
              {
                error: 'Invalid or missing school ID',
                message:
                  'Your user account has an invalid school reference. Please contact a super admin.',
                userRole: userRole,
                schoolData: mongoUser.school,
              },
              { status: 400 },
            )
          }

          // Get school data
          console.log('[School Profile API] Looking up school with ID:', schoolId)
          const school = await db.collection('schools').findOne({ _id: new ObjectId(schoolId) })

          if (!school) {
            return NextResponse.json({ error: 'School not found' }, { status: 404 })
          }

          // Get counts - Use the correct query structure based on actual data
          console.log('[School Profile API] Getting user counts for school:', schoolId)

          // Get role IDs first
          const studentRole = await db.collection('roles').findOne({ slug: 'student' })
          const teacherRole = await db.collection('roles').findOne({ slug: 'teacher' })
          const mentorRole = await db.collection('roles').findOne({ slug: 'mentor' })

          console.log('[School Profile API] Role IDs:', {
            student: studentRole?._id?.toString(),
            teacher: teacherRole?._id?.toString(),
            mentor: mentorRole?._id?.toString(),
          })

          // Count students - try both ObjectId and string formats
          let studentCount = 0
          if (studentRole) {
            // Try ObjectId format first (based on the sample data you provided)
            const studentCountQuery1 = {
              role: studentRole._id,
              school: new ObjectId(schoolId),
            }
            console.log(
              '[School Profile API] Student count query (ObjectId):',
              JSON.stringify(studentCountQuery1, (key, value) =>
                value instanceof ObjectId ? value.toString() : value,
              ),
            )
            studentCount = await db.collection('users').countDocuments(studentCountQuery1)
            console.log('[School Profile API] Student count result (ObjectId):', studentCount)

            // If no results, try string format
            if (studentCount === 0) {
              const studentCountQuery2 = {
                role: studentRole._id.toString(),
                school: schoolId.toString(),
              }
              console.log(
                '[School Profile API] Student count query (string):',
                JSON.stringify(studentCountQuery2),
              )
              studentCount = await db.collection('users').countDocuments(studentCountQuery2)
              console.log('[School Profile API] Student count result (string):', studentCount)
            }
          }

          // Count teachers - try both ObjectId and string formats
          let teacherCount = 0
          if (teacherRole) {
            // Try ObjectId format first (based on the sample data you provided)
            const teacherCountQuery1 = {
              role: teacherRole._id,
              school: new ObjectId(schoolId),
            }
            console.log(
              '[School Profile API] Teacher count query (ObjectId):',
              JSON.stringify(teacherCountQuery1, (key, value) =>
                value instanceof ObjectId ? value.toString() : value,
              ),
            )
            teacherCount = await db.collection('users').countDocuments(teacherCountQuery1)
            console.log('[School Profile API] Teacher count result (ObjectId):', teacherCount)

            // If no results, try string format
            if (teacherCount === 0) {
              const teacherCountQuery2 = {
                role: teacherRole._id.toString(),
                school: schoolId.toString(),
              }
              console.log(
                '[School Profile API] Teacher count query (string):',
                JSON.stringify(teacherCountQuery2),
              )
              teacherCount = await db.collection('users').countDocuments(teacherCountQuery2)
              console.log('[School Profile API] Teacher count result (string):', teacherCount)
            }
          }

          // Count mentors - try both ObjectId and string formats
          let mentorCount = 0
          if (mentorRole) {
            // Try ObjectId format first (based on the sample data you provided)
            const mentorCountQuery1 = {
              role: mentorRole._id,
              school: new ObjectId(schoolId),
            }
            console.log(
              '[School Profile API] Mentor count query (ObjectId):',
              JSON.stringify(mentorCountQuery1, (key, value) =>
                value instanceof ObjectId ? value.toString() : value,
              ),
            )
            mentorCount = await db.collection('users').countDocuments(mentorCountQuery1)
            console.log('[School Profile API] Mentor count result (ObjectId):', mentorCount)

            // If no results, try string format
            if (mentorCount === 0) {
              const mentorCountQuery2 = {
                role: mentorRole._id.toString(),
                school: schoolId.toString(),
              }
              console.log(
                '[School Profile API] Mentor count query (string):',
                JSON.stringify(mentorCountQuery2),
              )
              mentorCount = await db.collection('users').countDocuments(mentorCountQuery2)
              console.log('[School Profile API] Mentor count result (string):', mentorCount)
            }
          }

          // Count articles by finding users from this school and counting their articles
          let articleCount = 0

          // Get all users from this school - try both ObjectId and string formats
          let schoolUsers = await db
            .collection('users')
            .find({ school: new ObjectId(schoolId) })
            .toArray()
          console.log(
            '[School Profile API] Found school users (ObjectId query):',
            schoolUsers.length,
          )

          if (schoolUsers.length === 0) {
            // Try string format if ObjectId didn't work
            schoolUsers = await db
              .collection('users')
              .find({ school: schoolId.toString() })
              .toArray()
            console.log(
              '[School Profile API] Found school users (string query):',
              schoolUsers.length,
            )
          }

          if (schoolUsers.length > 0) {
            // Try both ObjectId and string formats for article author field
            const schoolUserObjectIds = schoolUsers.map((user) => user._id)
            const schoolUserStringIds = schoolUsers.map((user) => user._id.toString())

            // Try ObjectId format first (based on the sample data you provided)
            const articleCountQuery1 = {
              author: { $in: schoolUserObjectIds },
            }
            console.log(
              '[School Profile API] Article count query (ObjectId):',
              JSON.stringify(articleCountQuery1, (key, value) =>
                value instanceof ObjectId ? value.toString() : value,
              ),
            )
            articleCount = await db.collection('articles').countDocuments(articleCountQuery1)
            console.log('[School Profile API] Article count result (ObjectId):', articleCount)

            // If no results, try string format
            if (articleCount === 0) {
              const articleCountQuery2 = {
                author: { $in: schoolUserStringIds },
              }
              console.log(
                '[School Profile API] Article count query (string):',
                JSON.stringify(articleCountQuery2),
              )
              articleCount = await db.collection('articles').countDocuments(articleCountQuery2)
              console.log('[School Profile API] Article count result (string):', articleCount)
            }
          }

          // Format school data
          const formattedSchool = {
            id: school._id.toString(),
            name: school.name || '',
            address: school.address || '',
            city: school.city || '',
            state: school.state || '',
            zipCode: school.zipCode || '',
            country: school.country || '',
            phone: school.phone || '',
            email: school.email || '',
            website: school.website || '',
            description: school.description || '',
            image: school.image || null,
            imageUrl: school.imageUrl || null,
            createdAt: school.createdAt || new Date().toISOString(),
            updatedAt: school.updatedAt || new Date().toISOString(),
            admin: school.admin,
            studentCount,
            teacherCount,
            mentorCount,
            articleCount,
          }

          return NextResponse.json({ school: formattedSchool })
        }
      } catch (mongoError) {
        console.warn(
          'Error fetching school profile from MongoDB, falling back to Payload:',
          mongoError,
        )
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })
      console.log(
        '[School Profile API] Payload user found:',
        user ? 'yes' : 'no',
        'userId:',
        userId,
      )

      // Check if user is a school admin, mentor, teacher, or super admin
      let userRole = null
      console.log('[School Profile API] Payload user role raw:', user.role)

      if (user.role && typeof user.role === 'object' && user.role.slug) {
        userRole = user.role.slug
        console.log('[School Profile API] Payload user role from object:', userRole)
      } else if (typeof user.role === 'string') {
        // Try to look up the role document if it's an ObjectId string
        try {
          const roleDoc = await payload.findByID({ collection: 'roles', id: user.role })
          console.log('[School Profile API] Payload role lookup:', roleDoc)
          userRole = roleDoc?.slug || user.role
        } catch (error) {
          console.error('[School Profile API] Error in Payload role lookup:', error)
          userRole = user.role
        }
        console.log('[School Profile API] Payload user role from string:', userRole)
      }
      console.log('[School Profile API] Payload fallback - Final user role check:', userRole)

      // Modified condition to handle null roles differently
      if (!userRole) {
        console.log('[School Profile API] Permission denied: Role is null')
        return NextResponse.json({ error: 'Role not found or invalid' }, { status: 403 })
      }

      if (!['school-admin', 'mentor', 'teacher', 'super-admin'].includes(userRole)) {
        console.log(
          '[School Profile API] Payload fallback - Permission denied: User role is not allowed:',
          userRole,
        )
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get school ID
      let schoolId = null
      console.log('[School Profile API] User school raw value:', user.school)

      if (user.school) {
        if (typeof user.school === 'object') {
          // Cast to any to handle MongoDB properties that TypeScript doesn't know about
          const schoolObj = user.school as any
          if (schoolObj.id) {
            schoolId = schoolObj.id
          } else if (schoolObj.$oid) {
            schoolId = schoolObj.$oid
          } else if (schoolObj._id) {
            schoolId = schoolObj._id.toString()
          } else {
            schoolId = schoolObj.toString()
          }
        } else if (typeof user.school === 'string') {
          schoolId = user.school
        } else {
          // Cast to any to bypass TypeScript's type checking
          const schoolVal = user.school as any
          if (typeof schoolVal.toString === 'function') {
            schoolId = schoolVal.toString()
          }
        }
      }

      console.log('[School Profile API] Extracted school ID:', schoolId, 'Type:', typeof schoolId)

      // Validate the school ID before creating ObjectId
      if (!schoolId || !ObjectId.isValid(schoolId)) {
        console.error('[School Profile API] Invalid school ID:', schoolId)
        return NextResponse.json(
          {
            error: 'Invalid or missing school ID',
            message:
              'Your user account is not associated with a school. Please contact a super admin to assign you to a school.',
            userRole: userRole,
            schoolRaw: user.school,
          },
          { status: 400 },
        )
      }

      // Get school data
      const school = await payload.findByID({
        collection: 'schools',
        id: String(schoolId),
        depth: 1,
      })

      if (!school) {
        return NextResponse.json({ error: 'School not found' }, { status: 404 })
      }

      console.log(
        '[School Profile API] Payload school structure:',
        JSON.stringify(
          {
            id: school.id,
            fields: Object.keys(school),
            admin: school.admin,
          },
          null,
          2,
        ),
      )

      // Get counts
      const studentResult = await payload.find({
        collection: 'users',
        where: {
          'role.slug': {
            equals: 'student',
          },
          'school.id': {
            equals: String(schoolId),
          },
        },
        limit: 0,
      })
      console.log('[School Profile API] Payload student count result:', studentResult.totalDocs)

      // Try alternate way of querying for students
      try {
        // Use find instead of findBySlug
        const studentRoles = await payload.find({
          collection: 'roles',
          where: {
            slug: {
              equals: 'student',
            },
          },
          limit: 1,
        })

        if (studentRoles && studentRoles.docs && studentRoles.docs.length > 0) {
          const studentRole = studentRoles.docs[0]
          console.log('[School Profile API] Found student role by slug:', studentRole.id)

          const alternateStudentResult = await payload.find({
            collection: 'users',
            where: {
              role: {
                equals: studentRole.id,
              },
              'school.id': {
                equals: String(schoolId),
              },
            },
            limit: 0,
          })

          console.log(
            '[School Profile API] Alternate student count result:',
            alternateStudentResult.totalDocs,
          )
        }
      } catch (error) {
        console.log('[School Profile API] Error in alternate student count:', error)
      }

      const teacherResult = await payload.find({
        collection: 'users',
        where: {
          'role.slug': {
            equals: 'teacher',
          },
          'school.id': {
            equals: String(schoolId),
          },
        },
        limit: 0,
      })
      console.log('[School Profile API] Payload teacher count result:', teacherResult.totalDocs)

      const mentorResult = await payload.find({
        collection: 'users',
        where: {
          'role.slug': {
            equals: 'mentor',
          },
          'school.id': {
            equals: String(schoolId),
          },
        },
        limit: 0,
      })
      console.log('[School Profile API] Payload mentor count result:', mentorResult.totalDocs)

      // Fetch articles and filter by author's school
      let articleCount = 0
      try {
        const articlesResponse = await payload.find({
          collection: 'articles',
          depth: 1, // To populate author.school
          limit: 1000, // Adjust limit as needed, or implement pagination if too many
          pagination: false, // Get all (up to limit)
        })

        if (articlesResponse.docs) {
          articleCount = articlesResponse.docs.filter((article) => {
            if (typeof article.author === 'object' && article.author && article.author.school) {
              const authorSchoolId =
                typeof article.author.school === 'object'
                  ? article.author.school.id
                  : article.author.school
              return authorSchoolId === String(schoolId)
            }
            return false
          }).length
        }
      } catch (error) {
        console.warn('Error fetching or filtering articles count:', error)
      }

      // Format school data for Payload response
      const schoolImage = typeof school.image === 'object' ? school.image : null

      const formattedSchool = {
        id: school.id,
        name: school.name || '',
        address: school.address || '',
        city: school.city || '',
        state: school.state || '',
        zipCode: school.zipCode || '',
        country: school.country || '',
        phone: school.phone || '',
        email: school.email || '',
        website: school.website || '',
        description: school.description || '',
        image: schoolImage,
        imageUrl: school.imageUrl || null,
        createdAt: school.createdAt || new Date().toISOString(),
        updatedAt: school.updatedAt || new Date().toISOString(),
        admin: school.admin,
        studentCount: studentResult.totalDocs,
        teacherCount: teacherResult.totalDocs,
        mentorCount: mentorResult.totalDocs,
        articleCount,
      }

      return NextResponse.json({ school: formattedSchool })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching school profile:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token and get the user ID
      const { userId } = await verifyJWT(token)

      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      // Get the request body
      const body = await req.json()

      console.log('[School Profile API] PUT request body:', body)

      // Try to use MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB
        const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })
        console.log(
          '[School Profile API] MongoDB user found:',
          mongoUser ? 'yes' : 'no',
          'userId:',
          userId,
        )

        if (mongoUser) {
          // Check if user is a school admin or super admin
          let userRole = null
          console.log('[School Profile API] MongoDB user role raw:', mongoUser.role)

          if (mongoUser.role && typeof mongoUser.role === 'object' && mongoUser.role.slug) {
            userRole = mongoUser.role.slug
            console.log('[School Profile API] MongoDB user role from object:', userRole)
          } else if (typeof mongoUser.role === 'string') {
            // Try to look up the role document if it's an ObjectId string
            try {
              const roleDoc = await db
                .collection('roles')
                .findOne({ _id: new ObjectId(mongoUser.role) })
              console.log('[School Profile API] MongoDB role lookup:', roleDoc)
              userRole = roleDoc?.slug || mongoUser.role
            } catch (error) {
              console.error('[School Profile API] Error in role lookup:', error)
              userRole = mongoUser.role
            }
            console.log('[School Profile API] MongoDB user role from string:', userRole)
          } else if (mongoUser.role && typeof mongoUser.role === 'object') {
            // Handle role as ObjectId in various formats
            try {
              // Handle EJSON format with $oid
              if (mongoUser.role.$oid) {
                const roleDoc = await db
                  .collection('roles')
                  .findOne({ _id: new ObjectId(mongoUser.role.$oid) })
                console.log('[School Profile API] MongoDB role lookup from $oid:', roleDoc)
                userRole = roleDoc?.slug
              }
              // Handle direct ObjectId instance
              else if (
                mongoUser.role instanceof ObjectId ||
                (mongoUser.role.constructor && mongoUser.role.constructor.name === 'ObjectId')
              ) {
                const roleId = mongoUser.role.toString()
                console.log('[School Profile API] MongoDB role is direct ObjectId, ID:', roleId)
                const roleDoc = await db.collection('roles').findOne({ _id: new ObjectId(roleId) })
                console.log(
                  '[School Profile API] MongoDB role lookup from ObjectId instance:',
                  roleDoc,
                )
                userRole = roleDoc?.slug
              }
              // Handle _id field in embedded document
              else if (mongoUser.role._id) {
                const roleIdValue =
                  mongoUser.role._id instanceof ObjectId
                    ? mongoUser.role._id.toString()
                    : mongoUser.role._id.$oid || mongoUser.role._id
                console.log('[School Profile API] MongoDB role has _id field:', roleIdValue)
                const roleDoc = await db
                  .collection('roles')
                  .findOne({ _id: new ObjectId(roleIdValue) })
                console.log('[School Profile API] MongoDB role lookup from _id field:', roleDoc)
                userRole = roleDoc?.slug
              }
            } catch (error) {
              console.error('[School Profile API] Error in EJSON/ObjectId role lookup:', error)
            }
          }
          console.log('[School Profile API] Final user role check:', userRole)

          // Modified condition to handle null roles differently
          if (!userRole) {
            console.log('[School Profile API] Permission denied: Role is null')
            return NextResponse.json({ error: 'Role not found or invalid' }, { status: 403 })
          }

          if (!['school-admin', 'super-admin'].includes(userRole)) {
            console.log(
              '[School Profile API] Permission denied: User role is not allowed:',
              userRole,
            )
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get school ID from MongoDB EJSON format
          let schoolId = null
          console.log('[School Profile API] School data:', mongoUser.school)

          // Handle different ways school might be represented
          if (!mongoUser.school) {
            console.log('[School Profile API] No school found for user')
          } else if (typeof mongoUser.school === 'string') {
            schoolId = mongoUser.school
          } else if (typeof mongoUser.school === 'object') {
            // Use 'any' type for MongoDB data that TypeScript doesn't recognize
            const schoolObj = mongoUser.school as any

            // Check if it's in MongoDB EJSON format with $oid
            if (schoolObj.$oid) {
              schoolId = schoolObj.$oid
              console.log('[School Profile API] Found school ID in $oid format:', schoolId)
            } else if (schoolObj._id) {
              // Handle if it's a full embedded document
              const idField = schoolObj._id as any
              schoolId =
                typeof idField === 'object' && idField.$oid ? idField.$oid : String(idField)
              console.log('[School Profile API] Found school ID in _id field:', schoolId)
            } else if (schoolObj.id) {
              // Handle Payload format
              schoolId = schoolObj.id
              console.log('[School Profile API] Found school ID in id field:', schoolId)
            }
          }

          console.log('[School Profile API] Final school ID:', schoolId)

          if (!schoolId || !ObjectId.isValid(schoolId)) {
            console.error('[School Profile API] Invalid or missing school ID')
            return NextResponse.json(
              {
                error: 'Invalid or missing school ID',
                message:
                  'Your user account has an invalid school reference. Please contact a super admin.',
                userRole: userRole,
                schoolData: mongoUser.school,
              },
              { status: 400 },
            )
          }

          // Update school data
          const updateFields: {
            name?: string
            address?: string
            image?: any
            imageUrl?: string
            // Add all the new fields
            city?: string
            state?: string
            zipCode?: string
            country?: string
            phone?: string
            email?: string
            website?: string
            description?: string
            updatedAt: string
          } = {
            updatedAt: new Date().toISOString(),
          }

          // Add all fields that are provided in the request body
          if (body.name) updateFields.name = body.name
          if (body.address) updateFields.address = body.address
          if (body.imageUrl) updateFields.imageUrl = body.imageUrl
          // Only add image field if explicitly provided and not null
          if (body.image !== undefined && body.image !== null) {
            updateFields.image = body.image
          }

          // Add all additional fields if they are provided
          if (body.city) updateFields.city = body.city
          if (body.state) updateFields.state = body.state
          if (body.zipCode) updateFields.zipCode = body.zipCode
          if (body.country) updateFields.country = body.country
          if (body.phone) updateFields.phone = body.phone
          if (body.email) updateFields.email = body.email
          if (body.website) updateFields.website = body.website
          if (body.description) updateFields.description = body.description

          console.log('[School Profile API] Updating school with fields:', updateFields)

          const updateResult = await db.collection('schools').updateOne(
            { _id: new ObjectId(schoolId) },
            {
              $set: updateFields,
            },
          )

          if (updateResult.modifiedCount === 0 && updateResult.matchedCount === 0) {
            // if no document matched, it's a not found error
            return NextResponse.json(
              { error: 'School not found or no changes made' },
              { status: 404 },
            )
          }
          if (updateResult.modifiedCount === 0 && updateResult.matchedCount > 0) {
            // if a document matched but no changes were made
            return NextResponse.json({
              message: 'No changes detected in school profile',
              success: true,
              school: await db.collection('schools').findOne({ _id: new ObjectId(schoolId) }),
            })
          }

          // Get updated school data
          const updatedSchoolData = await db
            .collection('schools')
            .findOne({ _id: new ObjectId(schoolId) })

          if (!updatedSchoolData) {
            return NextResponse.json(
              { error: 'Failed to retrieve updated school data' },
              { status: 500 },
            )
          }

          // Format school data
          const formattedUpdatedSchool = {
            id: updatedSchoolData._id.toString(),
            name: updatedSchoolData.name || '',
            address: updatedSchoolData.address || '',
            city: updatedSchoolData.city || '',
            state: updatedSchoolData.state || '',
            zipCode: updatedSchoolData.zipCode || '',
            country: updatedSchoolData.country || '',
            phone: updatedSchoolData.phone || '',
            email: updatedSchoolData.email || '',
            website: updatedSchoolData.website || '',
            description: updatedSchoolData.description || '',
            image: updatedSchoolData.image || null,
            imageUrl: updatedSchoolData.imageUrl || null,
            admin: updatedSchoolData.admin,
            createdAt: updatedSchoolData.createdAt || new Date().toISOString(),
            updatedAt: updatedSchoolData.updatedAt || new Date().toISOString(),
          }

          return NextResponse.json({
            success: true,
            message: 'School profile updated successfully via MongoDB',
            school: formattedUpdatedSchool,
          })
        }
      } catch (mongoError) {
        console.warn(
          'Error updating school profile in MongoDB, falling back to Payload:',
          mongoError,
        )
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1, // user.school might be an ID or object
      })
      console.log(
        '[School Profile API] Payload user found:',
        user ? 'yes' : 'no',
        'userId:',
        userId,
      )

      // Check if user is a school admin or super admin
      let userRole = null
      console.log('[School Profile API] Payload user role raw:', user.role)

      if (user.role && typeof user.role === 'object' && user.role.slug) {
        userRole = user.role.slug
        console.log('[School Profile API] Payload user role from object:', userRole)
      } else if (typeof user.role === 'string') {
        // Try to look up the role document if it's an ObjectId string
        try {
          const roleDoc = await payload.findByID({ collection: 'roles', id: user.role })
          console.log('[School Profile API] Payload role lookup:', roleDoc)
          userRole = roleDoc?.slug || user.role
        } catch (error) {
          console.error('[School Profile API] Error in Payload role lookup:', error)
          userRole = user.role
        }
        console.log('[School Profile API] Payload user role from string:', userRole)
      }
      console.log('[School Profile API] Payload fallback - Final user role check:', userRole)

      // Modified condition to handle null roles differently
      if (!userRole) {
        console.log('[School Profile API] Permission denied: Role is null')
        return NextResponse.json({ error: 'Role not found or invalid' }, { status: 403 })
      }

      if (!['school-admin', 'super-admin'].includes(userRole)) {
        console.log(
          '[School Profile API] Payload fallback - Permission denied: User role is not allowed:',
          userRole,
        )
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get school ID
      let schoolId = null
      console.log('[School Profile API] User school raw value:', user.school)

      if (user.school) {
        if (typeof user.school === 'object') {
          // Cast to any to handle MongoDB properties that TypeScript doesn't know about
          const schoolObj = user.school as any
          if (schoolObj.id) {
            schoolId = schoolObj.id
          } else if (schoolObj.$oid) {
            schoolId = schoolObj.$oid
          } else if (schoolObj._id) {
            schoolId = schoolObj._id.toString()
          } else {
            schoolId = schoolObj.toString()
          }
        } else if (typeof user.school === 'string') {
          schoolId = user.school
        } else {
          // Cast to any to bypass TypeScript's type checking
          const schoolVal = user.school as any
          if (typeof schoolVal.toString === 'function') {
            schoolId = schoolVal.toString()
          }
        }
      }

      console.log('[School Profile API] Extracted school ID:', schoolId, 'Type:', typeof schoolId)

      // Validate the school ID before creating ObjectId
      if (!schoolId || !ObjectId.isValid(schoolId)) {
        console.error('[School Profile API] Invalid school ID:', schoolId)
        return NextResponse.json(
          {
            error: 'Invalid or missing school ID',
            message:
              'Your user account is not associated with a school. Please contact a super admin to assign you to a school.',
            userRole: userRole,
            schoolRaw: user.school,
          },
          { status: 400 },
        )
      }

      if (!schoolId) {
        return NextResponse.json({ error: 'User is not associated with a school' }, { status: 400 })
      }

      // Data to update - only existing fields
      const updateData: {
        name?: string
        address?: string
        admin?: string
        image?: string
        imageUrl?: string
        // Add all the new fields
        city?: string
        state?: string
        zipCode?: string
        country?: string
        phone?: string
        email?: string
        website?: string
        description?: string
      } = {}

      if (body.name) updateData.name = body.name
      if (body.address) updateData.address = body.address
      // Only add image field if explicitly provided and not null
      if (body.image !== undefined && body.image !== null) {
        updateData.image = body.image
      }
      if (body.imageUrl) updateData.imageUrl = body.imageUrl
      if (body.admin) updateData.admin = body.admin

      // Add all additional fields if they are provided
      if (body.city) updateData.city = body.city
      if (body.state) updateData.state = body.state
      if (body.zipCode) updateData.zipCode = body.zipCode
      if (body.country) updateData.country = body.country
      if (body.phone) updateData.phone = body.phone
      if (body.email) updateData.email = body.email
      if (body.website) updateData.website = body.website
      if (body.description) updateData.description = body.description

      console.log('[School Profile API] Payload update data:', updateData)

      if (Object.keys(updateData).length === 0) {
        // If no valid fields to update were provided, fetch and return current school data
        const currentSchool = await payload.findByID({
          collection: 'schools',
          id: String(schoolId),
        })
        return NextResponse.json({
          success: true,
          message: 'No valid fields provided for update.',
          school: currentSchool,
        })
      }

      // Update school data
      try {
        // Cast updateData to any to bypass TypeScript validation
        await payload.update({
          collection: 'schools',
          id: String(schoolId),
          data: updateData as any,
        })

        // Fetch the updated school to return the correct data
        const updatedSchoolByPayload = await payload.findByID({
          collection: 'schools',
          id: String(schoolId),
        })

        // Format school data
        const formattedPayloadSchool = {
          id: updatedSchoolByPayload.id,
          name: updatedSchoolByPayload.name || '',
          address: updatedSchoolByPayload.address || '',
          city: updatedSchoolByPayload.city || '',
          state: updatedSchoolByPayload.state || '',
          zipCode: updatedSchoolByPayload.zipCode || '',
          country: updatedSchoolByPayload.country || '',
          phone: updatedSchoolByPayload.phone || '',
          email: updatedSchoolByPayload.email || '',
          website: updatedSchoolByPayload.website || '',
          description: updatedSchoolByPayload.description || '',
          image: updatedSchoolByPayload.image || null,
          imageUrl: updatedSchoolByPayload.imageUrl || null,
          admin: updatedSchoolByPayload.admin,
          createdAt: updatedSchoolByPayload.createdAt || new Date().toISOString(),
          updatedAt: updatedSchoolByPayload.updatedAt || new Date().toISOString(),
        }

        return NextResponse.json({
          success: true,
          message: 'School profile updated successfully via Payload',
          school: formattedPayloadSchool,
        })
      } catch (error) {
        console.error('Error updating school via Payload:', error)
        return NextResponse.json({ error: 'Failed to update school profile' }, { status: 500 })
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating school profile:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

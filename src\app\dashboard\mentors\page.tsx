'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Star,
  MessageSquare,
  Calendar,
  Search,
  Pencil,
  Trash2,
  Loader2,
  Plus,
} from 'lucide-react'
import { Input } from '@/components/ui/input'
import { formatDistanceToNow } from 'date-fns'
import { useToast } from '@/components/ui/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import AddUserModal from '@/components/dashboard/AddUserModal'

interface Mentor {
  id: string
  name: string
  firstName?: string
  lastName?: string
  email: string
  school: string
  reviewCount: number
  averageRating: number
  lastActivity: string
}

export default function MentorsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [mentors, setMentors] = useState<Mentor[]>([])
  const [filteredMentors, setFilteredMentors] = useState<Mentor[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [selectedMentor, setSelectedMentor] = useState<Mentor | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [schoolId, setSchoolId] = useState('')
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
  })

  const fetchMentors = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/dashboard/mentors', {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch mentors')
      }

      const data = await response.json()
      const mentorsList = data.mentors || []
      setMentors(mentorsList)
      setFilteredMentors(mentorsList)

      setIsLoading(false)
    } catch (error) {
      console.error('Error fetching mentors:', error)
      setIsLoading(false)
    }
  }

  const fetchUserSchool = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      })

      if (response.ok) {
        const userData = await response.json()
        if (userData.user?.school) {
          setSchoolId(userData.user.school)
        }
      }
    } catch (err) {
      console.error('Error fetching user school:', err)
    }
  }

  useEffect(() => {
    fetchMentors()
    fetchUserSchool()
  }, [])

  const handleUserAdded = () => {
    // Refresh the mentors list
    fetchMentors()
  }

  const handleEditMentor = (mentor: Mentor) => {
    setSelectedMentor(mentor)
    const [firstName, lastName] = mentor.name.split(' ')
    setEditForm({
      firstName: firstName || '',
      lastName: lastName || '',
      email: mentor.email,
    })
    setShowEditDialog(true)
  }

  const handleDeleteMentor = (mentor: Mentor) => {
    setSelectedMentor(mentor)
    setShowDeleteDialog(true)
  }

  const confirmDeleteMentor = async () => {
    if (!selectedMentor) return

    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/dashboard/users/${selectedMentor.id}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete mentor')
      }

      toast({
        title: 'Mentor deleted',
        description: `${selectedMentor.name} has been deleted successfully.`,
      })

      fetchMentors()
      setShowDeleteDialog(false)
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to delete mentor. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const submitEditForm = async () => {
    if (!selectedMentor) return

    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/dashboard/users/${selectedMentor.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(editForm),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update mentor')
      }

      toast({
        title: 'Mentor updated',
        description: `${editForm.firstName} ${editForm.lastName}'s information has been updated successfully.`,
      })

      fetchMentors()
      setShowEditDialog(false)
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to update mentor. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Filter mentors based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredMentors(mentors)
    } else {
      const query = searchQuery.toLowerCase()
      const filtered = mentors.filter(
        (mentor) =>
          mentor.name.toLowerCase().includes(query) ||
          mentor.email.toLowerCase().includes(query) ||
          mentor.school.toLowerCase().includes(query),
      )
      setFilteredMentors(filtered)
    }
  }, [searchQuery, mentors])

  const getRatingBadge = (rating: number) => {
    if (rating >= 4.5) return <Badge className="bg-green-500">Excellent</Badge>
    if (rating >= 3.5) return <Badge className="bg-blue-500">Good</Badge>
    if (rating >= 2.5) return <Badge variant="outline">Average</Badge>
    return <Badge variant="destructive">Needs Improvement</Badge>
  }

  return (
    <DashboardLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">الموجهون</h1>
        <div className="flex gap-2">
          <Button onClick={() => setIsAddModalOpen(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            إضافة موجه جديد
          </Button>
          <Button onClick={() => router.push('/dashboard')}>العودة إلى لوحة التحكم</Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            إدارة الموجهين
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-5 w-5 text-gray-400" />
            <Input
              placeholder="البحث عن الموجهين بالاسم أو البريد الإلكتروني أو المدرسة..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الاسم</TableHead>
                    <TableHead>المدرسة</TableHead>
                    <TableHead>المراجعات</TableHead>
                    <TableHead>التقييم</TableHead>
                    <TableHead>آخر نشاط</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMentors.map((mentor) => (
                    <TableRow key={mentor.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{mentor.name}</div>
                          <div className="text-sm text-gray-500">{mentor.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>{mentor.school}</TableCell>
                      <TableCell>{mentor.reviewCount}</TableCell>
                      <TableCell>
                        {mentor.averageRating.toFixed(1)} {getRatingBadge(mentor.averageRating)}
                      </TableCell>
                      <TableCell>
                        {formatDistanceToNow(new Date(mentor.lastActivity), { addSuffix: true })}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditMentor(mentor)}
                            disabled={isSubmitting}
                          >
                            <Pencil className="h-4 w-4 mr-1" />
                            تعديل
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteMentor(mentor)}
                            className="text-red-500 hover:text-red-700"
                            disabled={isSubmitting}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            حذف
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}

                  {filteredMentors.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        لم يتم العثور على موجهين
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل معلومات الموجه</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">الاسم الأول</Label>
                <Input
                  id="firstName"
                  value={editForm.firstName}
                  onChange={(e) => setEditForm({ ...editForm, firstName: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">اسم العائلة</Label>
                <Input
                  id="lastName"
                  value={editForm.lastName}
                  onChange={(e) => setEditForm({ ...editForm, lastName: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input
                id="email"
                value={editForm.email}
                onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowEditDialog(false)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button onClick={submitEditForm} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري الحفظ...
                </>
              ) : (
                'حفظ التغييرات'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد الحذف</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              هل أنت متأكد من رغبتك في حذف الموجه{' '}
              <span className="font-medium">{selectedMentor?.name}</span>؟
            </p>
            <p className="text-sm text-gray-500 mt-2">
              هذا الإجراء لا يمكن التراجع عنه وسيؤدي إلى إزالة حساب الموجه وجميع البيانات المرتبطة
              به.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button variant="destructive" onClick={confirmDeleteMentor} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري الحذف...
                </>
              ) : (
                'تأكيد الحذف'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Mentor Modal */}
      <AddUserModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        schoolId={schoolId}
        defaultRole="mentor"
        onUserAdded={handleUserAdded}
      />
    </DashboardLayout>
  )
}

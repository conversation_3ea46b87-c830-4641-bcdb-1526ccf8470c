// Simple test to verify the ObjectId fix works
const { MongoClient, ObjectId } = require('mongodb');

async function testFix() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    const db = client.db('young-reporter');
    
    console.log('Testing ObjectId fix...');
    
    // Check existing students
    const existingStudents = await db.collection('users').find({
      role: new ObjectId('6817ce02750b3210d3bd2940'),
      school: new ObjectId('6817ce0724bc3cae11d7053a')
    }).toArray();
    
    console.log('Students with ObjectId role/school:', existingStudents.length);
    
    // Check students with string role/school
    const stringStudents = await db.collection('users').find({
      role: '6817ce02750b3210d3bd2940',
      school: '6817ce0724bc3cae11d7053a'
    }).toArray();
    
    console.log('Students with string role/school:', stringStudents.length);
    
    // Show all students for this school
    const allStudents = await db.collection('users').find({
      $or: [
        { school: new ObjectId('6817ce0724bc3cae11d7053a') },
        { school: '6817ce0724bc3cae11d7053a' }
      ]
    }).toArray();
    
    console.log('All students in school:', allStudents.length);
    allStudents.forEach(student => {
      console.log(`- ${student.firstName} ${student.lastName}: role=${typeof student.role}, school=${typeof student.school}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

testFix();

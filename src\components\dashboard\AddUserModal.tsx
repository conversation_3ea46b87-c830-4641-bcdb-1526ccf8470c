'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/components/ui/use-toast'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import ProfileImageUploadV2 from '@/components/ui/ProfileImageUploadV2'

interface AddUserModalProps {
  isOpen: boolean
  onClose: () => void
  schoolId: string
  defaultRole?: 'student' | 'teacher' | 'mentor'
  onUserAdded?: () => void
}

export default function AddUserModal({ 
  isOpen, 
  onClose, 
  schoolId, 
  defaultRole = 'student',
  onUserAdded 
}: AddUserModalProps) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    role: defaultRole,
    password: '',
    grade: '',
  })
  const [profileImageId, setProfileImageId] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleRoleChange = (value: string) => {
    setFormData(prev => ({ ...prev, role: value }))
  }

  const handleImageUpload = (imageId: string) => {
    setProfileImageId(imageId)
  }

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      role: defaultRole,
      password: '',
      grade: '',
    })
    setProfileImageId('')
    setShowPassword(false)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
      toast({
        title: 'خطأ في التحقق',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive',
      })
      return
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      toast({
        title: 'خطأ في البريد الإلكتروني',
        description: 'يرجى إدخال بريد إلكتروني صحيح',
        variant: 'destructive',
      })
      return
    }

    // Password validation (if provided)
    if (formData.password && formData.password.length < 8) {
      toast({
        title: 'خطأ في كلمة المرور',
        description: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
        variant: 'destructive',
      })
      return
    }
    
    try {
      setIsSubmitting(true)
      
      const requestData = {
        ...formData,
        schoolId,
        ...(profileImageId && { profileImageId }),
      }
      
      const response = await fetch('/api/dashboard/school-admin/create-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في إنشاء الحساب')
      }
      
      const data = await response.json()
      
      toast({
        title: 'تم إنشاء الحساب بنجاح',
        description: `تم إنشاء حساب ${formData.firstName} ${formData.lastName} بنجاح. ${data.tempPassword ? `كلمة المرور المؤقتة: ${data.tempPassword}` : ''}`,
      })
      
      resetForm()
      onClose()
      onUserAdded?.()
      
    } catch (error) {
      console.error('Error creating account:', error)
      toast({
        title: 'خطأ في إنشاء الحساب',
        description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      resetForm()
      onClose()
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'student': return 'طالب'
      case 'teacher': return 'معلم'
      case 'mentor': return 'موجه'
      default: return role
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle>إضافة {getRoleLabel(formData.role)} جديد</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Profile Image Upload */}
          <div className="flex justify-center">
            <ProfileImageUploadV2
              onImageUpload={handleImageUpload}
              firstName={formData.firstName}
              lastName={formData.lastName}
            />
          </div>

          {/* First Name */}
          <div>
            <Label htmlFor="firstName">الاسم الأول *</Label>
            <Input
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Last Name */}
          <div>
            <Label htmlFor="lastName">الاسم الأخير *</Label>
            <Input
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Email */}
          <div>
            <Label htmlFor="email">البريد الإلكتروني *</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Role */}
          <div>
            <Label htmlFor="role">الدور *</Label>
            <Select value={formData.role} onValueChange={handleRoleChange} disabled={isSubmitting}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="student">طالب</SelectItem>
                <SelectItem value="teacher">معلم</SelectItem>
                <SelectItem value="mentor">موجه</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Grade (for students only) */}
          {formData.role === 'student' && (
            <div>
              <Label htmlFor="grade">الصف</Label>
              <Input
                id="grade"
                name="grade"
                value={formData.grade}
                onChange={handleChange}
                placeholder="مثال: الصف العاشر"
                disabled={isSubmitting}
              />
            </div>
          )}

          {/* Password */}
          <div>
            <Label htmlFor="password">كلمة المرور (اختيارية)</Label>
            <div className="relative">
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleChange}
                placeholder="إذا تركت فارغة، سيتم إنشاء كلمة مرور مؤقتة"
                disabled={isSubmitting}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isSubmitting}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {formData.password && formData.password.length < 8 && (
              <p className="text-sm text-red-500 mt-1">كلمة المرور يجب أن تكون 8 أحرف على الأقل</p>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1"
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري الإنشاء...
                </>
              ) : (
                'إنشاء الحساب'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

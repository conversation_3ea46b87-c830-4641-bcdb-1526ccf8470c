import { NextResponse } from 'next/server'
import { getPayload } from 'payload' // Corrected import for getPayload
import config from '../../../payload.config' // Import the payload config
// Removed explicit Where import, relying on type inference

export async function GET(request: Request) {
  try {
    const payload = await getPayload({ config }) // Correct way to get payload instance
    const url = new URL(request.url)

    // Get query parameters
    const schoolIdParam = url.searchParams.get('schoolId')
    // const userRoleParam = url.searchParams.get('userRole') // userRole might not be a direct query field in Activities
    const userIdParam = url.searchParams.get('userId')
    const activityTypeParam = url.searchParams.get('activityType')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')

    // Build Payload 'where' query
    const where: Record<string, any> = {} // Using a general type, or let it be inferred
    const conditions = []

    if (schoolIdParam) {
      conditions.push({ school: { equals: schoolIdParam } })
    }

    if (userIdParam) {
      conditions.push({ userId: { equals: userIdParam } })
    }

    if (activityTypeParam) {
      conditions.push({ activityType: { equals: activityTypeParam } })
    }

    // If there are multiple conditions, wrap them in an 'and'
    if (conditions.length > 0) {
      where.and = conditions
    }

    console.log('Activities API - Query:', where)

    const activitiesData = await payload.find({
      collection: 'activities',
      where: Object.keys(where).length > 0 ? where : undefined, // Pass undefined if no specific where clause
      sort: '-createdAt', // Sort by createdAt descending
      page,
      limit,
      depth: 2, // Increase depth to populate relationships (users and their roles)
    })

    console.log('Activities API - Raw results count:', activitiesData.docs.length)

    // Map the activities to include the expected fields for the UI
    const formattedActivities = await Promise.all(
      activitiesData.docs.map(async (activity) => {
        // Default values
        let userName = 'Unknown'
        let userRole = 'Unknown'
        let description = 'No description'
        let date = activity.createdAt

        // Extract user name and role if possible
        if (activity.userId) {
          try {
            // If userId is already populated
            if (typeof activity.userId === 'object' && activity.userId !== null) {
              const user = activity.userId
              userName =
                `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email || 'Unknown'

              // Extract role
              if (user.role) {
                if (typeof user.role === 'object' && user.role !== null) {
                  userRole = user.role.slug || 'Unknown'
                } else {
                  // Try to look up the role
                  try {
                    const roleDoc = await payload.findByID({
                      collection: 'roles',
                      id: user.role.toString(),
                    })
                    userRole = roleDoc?.slug || 'Unknown'
                  } catch (e) {
                    console.error('Error looking up role:', e)
                  }
                }
              }
            }
            // If userId is just an ID, fetch the user
            else if (activity.userId) {
              try {
                const user = await payload.findByID({
                  collection: 'users',
                  id: activity.userId.toString(),
                  depth: 1,
                })

                if (user) {
                  userName =
                    `${user.firstName || ''} ${user.lastName || ''}`.trim() ||
                    user.email ||
                    'Unknown'

                  // Extract role
                  if (user.role) {
                    if (typeof user.role === 'object' && user.role !== null) {
                      userRole = user.role.slug || 'Unknown'
                    } else {
                      // Try to look up the role
                      try {
                        const roleDoc = await payload.findByID({
                          collection: 'roles',
                          id: user.role.toString(),
                        })
                        userRole = roleDoc?.slug || 'Unknown'
                      } catch (e) {
                        console.error('Error looking up role:', e)
                      }
                    }
                  }
                }
              } catch (e) {
                console.error('Error fetching user:', e)
              }
            }
          } catch (e) {
            console.error('Error processing user data:', e)
          }
        }

        // Generate a human-readable description based on activity type (in Arabic)
        switch (activity.activityType) {
          case 'article-review':
            description = 'راجع مقالاً'
            break
          case 'article-comment':
            description = 'علق على مقال'
            break
          case 'student-approval':
            description = 'وافق على حساب طالب'
            break
          case 'profile-image-approval':
            description = 'وافق على صورة الملف الشخصي'
            break
          case 'name-change-approval':
            description = 'وافق على تغيير الاسم'
            break
          case 'news-post':
            description = 'نشر خبراً'
            break
          case 'achievement-earned':
            description = 'حصل على إنجاز'
            break
          case 'login':
            description = 'سجل دخول إلى المنصة'
            break
          case 'article-report':
            description = 'أبلغ عن مقال'
            break
          default:
            description = 'قام بنشاط'
        }

        // Add more details from the activity.details if available
        if (activity.details) {
          try {
            const details =
              typeof activity.details === 'string' ? JSON.parse(activity.details) : activity.details

            if (details.description) {
              description = details.description
            }

            // Handle specific details based on activity type (in Arabic)
            if (activity.activityType === 'article-review' && details.articleTitle) {
              description = `راجع مقال: ${details.articleTitle}`
            } else if (activity.activityType === 'article-comment' && details.articleTitle) {
              description = `علق على مقال: ${details.articleTitle}`
            } else if (activity.activityType === 'article-report' && details.articleTitle) {
              description = `أبلغ عن مقال: ${details.articleTitle}`
            }
          } catch (e) {
            console.error('Error parsing activity details:', e)
          }
        }

        return {
          id: activity.id,
          userId: typeof activity.userId === 'object' ? activity.userId.id : activity.userId,
          userName,
          userRole,
          schoolId: typeof activity.school === 'object' ? activity.school?.id : activity.school,
          schoolName: typeof activity.school === 'object' ? activity.school?.name : undefined,
          activityType: activity.activityType,
          description,
          date,
          targetId:
            typeof activity.targetUserId === 'object'
              ? activity.targetUserId?.id
              : activity.targetUserId,
          targetType: 'user', // Default to user for targetUserId
          points: activity.points || 0,
        }
      }),
    )

    console.log('Activities API - Formatted results count:', formattedActivities.length)

    return NextResponse.json({
      docs: formattedActivities,
      totalDocs: activitiesData.totalDocs,
      limit: activitiesData.limit,
      totalPages: activitiesData.totalPages,
      page: activitiesData.page,
      pagingCounter: activitiesData.pagingCounter,
      hasPrevPage: activitiesData.hasPrevPage,
      hasNextPage: activitiesData.hasNextPage,
      prevPage: activitiesData.prevPage,
      nextPage: activitiesData.nextPage,
    })
  } catch (error) {
    let errorMessage = 'Failed to fetch activities'
    if (error instanceof Error) {
      errorMessage = error.message
    }
    console.error('Error in activities API:', error)
    return NextResponse.json({ error: errorMessage }, { status: 500 })
  }
}

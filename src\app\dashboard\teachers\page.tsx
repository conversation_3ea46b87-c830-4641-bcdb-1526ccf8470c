'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Eye, ThumbsUp, Activity, FileText, User, Pencil, Trash2, Plus } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import AddUserModal from '@/components/dashboard/AddUserModal'

interface Teacher {
  id: string
  firstName: string
  lastName: string
  email: string
  school?: {
    id: string
    name: string
  }
  stats?: {
    studentsApproved: number
    articlesReviewed: number
  }
}

export default function TeachersPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [teacherActivities, setTeacherActivities] = useState<Record<string, any[]>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [schoolId, setSchoolId] = useState('')
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
  })

  const fetchData = async () => {
    try {
      setIsLoading(true)

      // Fetch teachers
      const teachersResponse = await fetch('/api/dashboard/teachers', {
        credentials: 'include',
      })

      if (!teachersResponse.ok) {
        throw new Error('فشل في جلب بيانات المعلمين')
      }

      const teachersData = await teachersResponse.json()
      const teachersList = teachersData.teachers || []
      setTeachers(teachersList)

      // Fetch recent activities for each teacher
      const activitiesData: Record<string, any[]> = {}

      for (const teacher of teachersList) {
        try {
          const activitiesResponse = await fetch(
            `/api/dashboard/teachers/activities?teacherId=${teacher.id}&limit=5`,
            {
              credentials: 'include',
            },
          )

          if (activitiesResponse.ok) {
            const data = await activitiesResponse.json()
            activitiesData[teacher.id] = data.activities || []
          }
        } catch (activityError) {
          console.error(`Error fetching activities for teacher ${teacher.id}:`, activityError)
          // Continue with other teachers even if one fails
        }
      }

      setTeacherActivities(activitiesData)
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching data:', err)
      setError('فشل في تحميل بيانات المعلمين. يرجى المحاولة مرة أخرى.')
      setIsLoading(false)
    }
  }

  const fetchUserSchool = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      })

      if (response.ok) {
        const userData = await response.json()
        if (userData.user?.school) {
          setSchoolId(userData.user.school)
        }
      }
    } catch (err) {
      console.error('Error fetching user school:', err)
    }
  }

  useEffect(() => {
    fetchData()
    fetchUserSchool()
  }, [])

  const handleUserAdded = () => {
    // Refresh the teachers list
    fetchData()
  }

  const handleEditTeacher = (teacher: Teacher) => {
    setSelectedTeacher(teacher)
    setEditForm({
      firstName: teacher.firstName,
      lastName: teacher.lastName,
      email: teacher.email,
    })
    setShowEditDialog(true)
  }

  const handleDeleteTeacher = (teacher: Teacher) => {
    setSelectedTeacher(teacher)
    setShowDeleteDialog(true)
  }

  const confirmDeleteTeacher = async () => {
    if (!selectedTeacher) return

    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/dashboard/users/${selectedTeacher.id}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في حذف المعلم')
      }

      toast({
        title: 'تم حذف المعلم',
        description: `تم حذف ${selectedTeacher.firstName} ${selectedTeacher.lastName} بنجاح.`,
      })

      // Refresh the teacher list
      fetchData()
      setShowDeleteDialog(false)
    } catch (err: any) {
      toast({
        title: 'خطأ',
        description: err.message || 'فشل في حذف المعلم. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const submitEditForm = async () => {
    if (!selectedTeacher) return

    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/dashboard/users/${selectedTeacher.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(editForm),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تحديث بيانات المعلم')
      }

      toast({
        title: 'تم تحديث بيانات المعلم',
        description: `تم تحديث معلومات ${editForm.firstName} ${editForm.lastName} بنجاح.`,
      })

      // Refresh the teacher list
      fetchData()
      setShowEditDialog(false)
    } catch (err: any) {
      toast({
        title: 'خطأ',
        description: err.message || 'فشل في تحديث بيانات المعلم. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64" dir="rtl">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="p-6" dir="rtl">
          <Card className="border-red-200">
            <CardContent className="pt-6">
              <div className="text-center text-red-500">
                <p className="mb-4">{error}</p>
                <Button onClick={() => fetchData()}>إعادة المحاولة</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6" dir="rtl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">المعلمين</h1>
          <Button onClick={() => setIsAddModalOpen(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            إضافة معلم جديد
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>قائمة المعلمين</CardTitle>
          </CardHeader>
          <CardContent>
            {teachers.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>المعلم</TableHead>
                      <TableHead>البريد الإلكتروني</TableHead>
                      <TableHead>المدرسة</TableHead>
                      <TableHead>الإحصائيات</TableHead>
                      <TableHead className="text-left">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {teachers.map((teacher) => (
                      <TableRow key={teacher.id}>
                        <TableCell className="font-medium">
                          {teacher.firstName} {teacher.lastName}
                        </TableCell>
                        <TableCell>{teacher.email}</TableCell>
                        <TableCell>{teacher.school?.name || 'غير محدد'}</TableCell>
                        <TableCell>
                          <div className="flex space-x-4 space-x-reverse">
                            <div className="flex items-center">
                              <ThumbsUp className="h-4 w-4 text-blue-500 ml-1" />
                              <span>{teacher.stats?.studentsApproved || 0}</span>
                            </div>
                            <div className="flex items-center">
                              <FileText className="h-4 w-4 text-green-500 ml-1" />
                              <span>{teacher.stats?.articlesReviewed || 0}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2 space-x-reverse">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => router.push(`/dashboard/teachers/${teacher.id}`)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleEditTeacher(teacher)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleDeleteTeacher(teacher)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                لا يوجد معلمين مسجلين. قم بإضافة معلمين جدد للبدء.
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Teacher Dialog */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent dir="rtl">
            <DialogHeader>
              <DialogTitle>تعديل بيانات المعلم</DialogTitle>
              <DialogDescription>
                قم بتعديل معلومات المعلم أدناه. اضغط حفظ عند الانتهاء.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">الاسم الأول</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    value={editForm.firstName}
                    onChange={(e) => setEditForm({ ...editForm, firstName: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">الاسم الأخير</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    value={editForm.lastName}
                    onChange={(e) => setEditForm({ ...editForm, lastName: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={editForm.email}
                  onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter className="gap-2 sm:justify-start">
              <Button type="submit" onClick={submitEditForm} disabled={isSubmitting}>
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowEditDialog(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Teacher Dialog */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent dir="rtl">
            <DialogHeader>
              <DialogTitle>تأكيد حذف المعلم</DialogTitle>
              <DialogDescription>
                هل أنت متأكد من رغبتك في حذف هذا المعلم؟ لا يمكن التراجع عن هذا الإجراء.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="gap-2 sm:justify-start">
              <Button variant="destructive" onClick={confirmDeleteTeacher} disabled={isSubmitting}>
                {isSubmitting ? 'جاري الحذف...' : 'تأكيد الحذف'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowDeleteDialog(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Teacher Modal */}
        <AddUserModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          schoolId={schoolId}
          defaultRole="teacher"
          onUserAdded={handleUserAdded}
        />
      </div>
    </DashboardLayout>
  )
}
